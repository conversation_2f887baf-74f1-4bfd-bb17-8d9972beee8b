#!/usr/bin/env python3
"""
Demo script for female voice AI response system.
Shows how to detect user language and respond with appropriate female voice.
"""

import os
import sys
import asyncio
import tempfile
from pathlib import Path

# Add the tutor directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tutor'))

from tutor.modules.audio.openai_tts import OpenAITTS, OpenAITTSError
from tutor.modules.models.classes import env

def detect_language(text: str) -> str:
    """
    Detect language from text using character analysis.
    
    Args:
        text: Text to analyze
        
    Returns:
        Language code ('hi' for Hindi, 'en' for English)
    """
    if not text or not text.strip():
        return 'en'
    
    # Count Devanagari characters (Hindi script)
    devanagari_chars = 0
    total_chars = 0
    
    for char in text:
        if char.isalpha():
            total_chars += 1
            # Check if character is in Devanagari range (Hindi)
            if 0x0900 <= ord(char) <= 0x097F:
                devanagari_chars += 1
    
    if total_chars == 0:
        return 'en'
    
    # If more than 30% of characters are Devanagari, consider it Hindi
    hindi_ratio = devanagari_chars / total_chars
    return 'hi' if hindi_ratio > 0.3 else 'en'

def generate_ai_response(user_input: str, detected_language: str) -> str:
    """
    Generate AI response in the same language as user input.
    
    Args:
        user_input: User's input text
        detected_language: Detected language code
        
    Returns:
        AI response text in appropriate language
    """
    if detected_language == 'hi':
        # Hindi responses
        if 'नमस्ते' in user_input or 'हैलो' in user_input:
            return "नमस्ते! मैं आपकी AI सहायक हूँ। मैं आपकी सहायता के लिए यहाँ हूँ।"
        elif 'कैसे' in user_input and 'हैं' in user_input:
            return "मैं बहुत अच्छी हूँ, धन्यवाद! आप कैसे हैं? मैं आपकी क्या सहायता कर सकती हूँ?"
        elif 'मदद' in user_input or 'सहायता' in user_input:
            return "जी हाँ, मैं आपकी मदद करने के लिए तैयार हूँ। कृपया बताएं कि आपको क्या चाहिए।"
        elif 'धन्यवाद' in user_input:
            return "आपका स्वागत है! क्या मैं आपकी और कोई सहायता कर सकती हूँ?"
        elif 'मौसम' in user_input:
            return "मुझे खुशी होगी अगर मैं मौसम की जानकारी दे सकूं, लेकिन मुझे वर्तमान मौसम डेटा तक पहुंच नहीं है।"
        else:
            return "यह एक दिलचस्प सवाल है। मैं आपकी सहायता करने की कोशिश करूंगी।"
    else:
        # English responses
        if 'hello' in user_input.lower() or 'hi' in user_input.lower():
            return "Hello! I'm your AI assistant. I'm here to help you with anything you need."
        elif 'how are you' in user_input.lower():
            return "I'm doing great, thank you! How are you today? How can I assist you?"
        elif 'help' in user_input.lower() or 'assist' in user_input.lower():
            return "Of course! I'm ready to help you. Please let me know what you need assistance with."
        elif 'thank' in user_input.lower():
            return "You're very welcome! Is there anything else I can help you with?"
        elif 'weather' in user_input.lower():
            return "I'd be happy to help with weather information, but I don't have access to current weather data."
        else:
            return "That's an interesting question. I'll do my best to help you with that."

async def process_user_input(user_input: str, save_audio: bool = False) -> tuple[str, str, str]:
    """
    Process user input and generate AI response with female voice.
    
    Args:
        user_input: User's input text
        save_audio: Whether to save audio to file
        
    Returns:
        tuple: (detected_language, ai_response, audio_file_path or None)
    """
    # Detect language
    detected_language = detect_language(user_input)
    print(f"🔍 Detected language: {'Hindi' if detected_language == 'hi' else 'English'}")
    
    # Generate AI response
    ai_response = generate_ai_response(user_input, detected_language)
    print(f"🤖 AI Response: {ai_response}")
    
    # Generate audio with female voice
    try:
        tts = OpenAITTS()
        audio_stream = tts.generate_audio(ai_response, detected_language)
        
        audio_file_path = None
        if save_audio:
            # Save audio to temporary file
            temp_dir = tempfile.gettempdir()
            audio_file_path = os.path.join(temp_dir, f"ai_response_{detected_language}.mp3")
            
            with open(audio_file_path, "wb") as f:
                f.write(audio_stream.read())
            
            print(f"🎵 Audio saved to: {audio_file_path}")
        else:
            audio_data = audio_stream.read()
            print(f"🎵 Audio generated: {len(audio_data)} bytes with female voice")
        
        return detected_language, ai_response, audio_file_path
        
    except OpenAITTSError as e:
        print(f"❌ TTS Error: {e}")
        return detected_language, ai_response, None
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return detected_language, ai_response, None

async def interactive_demo():
    """Run interactive demo of the female voice AI system."""
    print("🎤 Female Voice AI Response Demo")
    print("=" * 40)
    print("This demo shows how the AI detects your language and responds with a female voice.")
    print("Type 'quit' to exit.\n")
    
    # Check API key
    api_key = env.openai_api_key or os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OpenAI API key not found. Please set OPENAI_API_KEY environment variable.")
        return
    
    print(f"✓ Using female voices: English='{env.openai_tts_voice_english}', Hindi='{env.openai_tts_voice_hindi}'\n")
    
    while True:
        try:
            # Get user input
            user_input = input("👤 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("👋 Goodbye!")
                break
            
            if not user_input:
                continue
            
            print()  # Add spacing
            
            # Process input and generate response
            detected_lang, ai_response, audio_file = await process_user_input(user_input, save_audio=True)
            
            if audio_file:
                print(f"💾 Audio file saved for playback: {audio_file}")
            
            print("-" * 40)
            print()
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

async def run_sample_conversations():
    """Run sample conversations to demonstrate the system."""
    print("🎭 Sample Conversations Demo")
    print("=" * 40)
    
    sample_conversations = [
        # English conversation
        ("Hello, how are you?", "English conversation"),
        ("Can you help me with something?", ""),
        ("Thank you for your assistance!", ""),
        
        # Hindi conversation  
        ("नमस्ते, आप कैसे हैं?", "Hindi conversation"),
        ("क्या आप मेरी मदद कर सकते हैं?", ""),
        ("आपकी सहायता के लिए धन्यवाद!", ""),
        
        # Mixed examples
        ("What's the weather like today?", "Weather inquiry"),
        ("आज मौसम कैसा है?", "मौसम की जानकारी"),
    ]
    
    for user_input, context in sample_conversations:
        if context:
            print(f"\n--- {context} ---")
        
        print(f"👤 User: {user_input}")
        
        detected_lang, ai_response, _ = await process_user_input(user_input, save_audio=False)
        
        print("-" * 30)

async def main():
    """Main function to run the demo."""
    print("🚀 Female Voice AI Response System Demo\n")
    
    # Check API key
    api_key = env.openai_api_key or os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OpenAI API key not found.")
        print("Please set your OpenAI API key:")
        print("   export OPENAI_API_KEY='your-api-key-here'")
        print("   or add it to your .env file")
        print("\nYou can get an API key from: https://platform.openai.com/api-keys")
        return
    
    print("Choose demo mode:")
    print("1. Interactive demo (type and get responses)")
    print("2. Sample conversations (automated demo)")
    print("3. Both")
    
    try:
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            await interactive_demo()
        elif choice == "2":
            await run_sample_conversations()
        elif choice == "3":
            await run_sample_conversations()
            print("\n" + "="*50)
            await interactive_demo()
        else:
            print("Invalid choice. Running sample conversations...")
            await run_sample_conversations()
            
    except KeyboardInterrupt:
        print("\n👋 Demo ended.")

if __name__ == "__main__":
    asyncio.run(main())
