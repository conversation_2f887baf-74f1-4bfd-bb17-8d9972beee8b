#!/usr/bin/env python3
"""
Test script for female voice AI response with multilingual support.
Tests language detection and appropriate voice response in the same language.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add the tutor directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tutor'))

from tutor.modules.audio.openai_tts import OpenAITTS, OpenAITTSError
from tutor.modules.audio import text_speech
from tutor.modules.models.classes import env

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def detect_language_simple(text: str) -> str:
    """
    Simple language detection based on character analysis.
    
    Args:
        text: Text to analyze
        
    Returns:
        Language code ('hi' for Hindi, 'en' for English)
    """
    if not text or not text.strip():
        return 'en'  # Default to English for empty text
    
    # Count Devanagari characters (Hindi script)
    devanagari_chars = 0
    latin_chars = 0
    total_chars = 0
    
    for char in text:
        if char.isalpha():
            total_chars += 1
            # Check if character is in Devanagari range (Hindi)
            if 0x0900 <= ord(char) <= 0x097F:
                devanagari_chars += 1
            # Check if character is in Latin range (English)
            elif 0x0041 <= ord(char) <= 0x007A:
                latin_chars += 1
    
    if total_chars == 0:
        return 'en'  # Default to English if no alphabetic characters
    
    # If more than 30% of characters are Devanagari, consider it Hindi
    hindi_ratio = devanagari_chars / total_chars
    if hindi_ratio > 0.3:
        return 'hi'
    else:
        return 'en'

def test_voice_configuration():
    """Test that female voices are configured correctly."""
    print("🔧 Testing voice configuration...")
    
    print(f"English voice: {env.openai_tts_voice_english}")
    print(f"Hindi voice: {env.openai_tts_voice_hindi}")
    
    # Check if female voices are configured
    female_voices = ['nova', 'shimmer']
    
    if env.openai_tts_voice_english in female_voices:
        print(f"✓ English voice '{env.openai_tts_voice_english}' is female")
    else:
        print(f"⚠ English voice '{env.openai_tts_voice_english}' is not female. Female voices: {female_voices}")
    
    if env.openai_tts_voice_hindi in female_voices:
        print(f"✓ Hindi voice '{env.openai_tts_voice_hindi}' is female")
    else:
        print(f"⚠ Hindi voice '{env.openai_tts_voice_hindi}' is not female. Female voices: {female_voices}")
    
    return True

def test_language_detection():
    """Test language detection functionality."""
    print("\n🔍 Testing language detection...")
    
    test_cases = [
        ("Hello, how are you today?", "en"),
        ("नमस्ते, आप कैसे हैं?", "hi"),
        ("मैं ठीक हूँ, धन्यवाद।", "hi"),
        ("Thank you for your help.", "en"),
        ("आपका स्वागत है।", "hi"),
        ("Good morning!", "en"),
        ("शुभ प्रभात!", "hi"),
        ("", "en"),  # Empty text should default to English
    ]
    
    all_passed = True
    for text, expected_lang in test_cases:
        detected_lang = detect_language_simple(text)
        if detected_lang == expected_lang:
            print(f"✓ '{text}' -> {detected_lang}")
        else:
            print(f"✗ '{text}' -> {detected_lang} (expected {expected_lang})")
            all_passed = False
    
    return all_passed

def test_openai_tts_direct():
    """Test OpenAI TTS directly with female voices."""
    print("\n🎤 Testing OpenAI TTS with female voices...")
    
    try:
        tts = OpenAITTS()
        
        # Test English with female voice
        english_text = "Hello, I am your AI assistant speaking with a female voice."
        print(f"Generating English audio: '{english_text}'")
        
        audio_stream = tts.generate_audio(english_text, 'en')
        audio_data = audio_stream.read()
        print(f"✓ English audio generated: {len(audio_data)} bytes")
        
        # Test Hindi with female voice
        hindi_text = "नमस्ते, मैं आपकी AI सहायक हूँ और मैं महिला आवाज़ में बोल रही हूँ।"
        print(f"Generating Hindi audio: '{hindi_text}'")
        
        audio_stream_hindi = tts.generate_audio(hindi_text, 'hi')
        audio_data_hindi = audio_stream_hindi.read()
        print(f"✓ Hindi audio generated: {len(audio_data_hindi)} bytes")
        
        return True
        
    except OpenAITTSError as e:
        print(f"✗ OpenAI TTS Error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

async def test_async_tts():
    """Test async TTS functionality."""
    print("\n⚡ Testing async TTS functionality...")
    
    try:
        # Test English async
        english_text = "This is an async test of the female voice in English."
        print(f"Testing async English: '{english_text}'")
        
        audio_stream = await text_speech.text_to_audio(english_text, 'en')
        audio_data = audio_stream.read()
        print(f"✓ Async English audio: {len(audio_data)} bytes")
        
        # Test Hindi async
        hindi_text = "यह हिंदी में महिला आवाज़ का async टेस्ट है।"
        print(f"Testing async Hindi: '{hindi_text}'")
        
        audio_stream_hindi = await text_speech.text_to_audio(hindi_text, 'hi')
        audio_data_hindi = audio_stream_hindi.read()
        print(f"✓ Async Hindi audio: {len(audio_data_hindi)} bytes")
        
        return True
        
    except Exception as e:
        print(f"✗ Async TTS error: {e}")
        return False

def test_integrated_response_system():
    """Test the complete response system with language detection and appropriate voice."""
    print("\n🤖 Testing integrated AI response system...")
    
    def generate_ai_response(user_input: str) -> tuple[str, str]:
        """
        Simulate AI response generation based on user input language.
        
        Returns:
            tuple: (response_text, language_code)
        """
        detected_lang = detect_language_simple(user_input)
        
        if detected_lang == 'hi':
            # Generate Hindi response
            responses = [
                "नमस्ते! मैं आपकी सहायता के लिए यहाँ हूँ।",
                "आपका प्रश्न बहुत अच्छा है। मैं इसका उत्तर दे सकती हूँ।",
                "धन्यवाद! क्या मैं आपकी और कोई सहायता कर सकती हूँ?"
            ]
        else:
            # Generate English response
            responses = [
                "Hello! I'm here to help you with your questions.",
                "That's a great question. I'd be happy to assist you.",
                "Thank you! Is there anything else I can help you with?"
            ]
        
        # Simple response selection based on input
        response = responses[len(user_input) % len(responses)]
        return response, detected_lang
    
    test_inputs = [
        "Hello, can you help me?",
        "नमस्ते, क्या आप मेरी मदद कर सकते हैं?",
        "What is the weather like today?",
        "आज मौसम कैसा है?",
        "Thank you for your assistance.",
        "आपकी सहायता के लिए धन्यवाद।"
    ]
    
    try:
        tts = OpenAITTS()
        all_passed = True
        
        for user_input in test_inputs:
            print(f"\n👤 User: {user_input}")
            
            # Detect language and generate response
            response_text, response_lang = generate_ai_response(user_input)
            print(f"🔍 Detected language: {response_lang}")
            print(f"🤖 AI Response: {response_text}")
            
            # Generate audio with appropriate female voice
            try:
                audio_stream = tts.generate_audio(response_text, response_lang)
                audio_data = audio_stream.read()
                print(f"🎵 Audio generated: {len(audio_data)} bytes with {response_lang} female voice")
            except Exception as e:
                print(f"✗ Audio generation failed: {e}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"✗ Integrated test error: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 Starting Female Voice Multilingual AI Response Tests\n")
    
    # Check if OpenAI API key is available
    api_key = env.openai_api_key or os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OpenAI API key not found. Please set OPENAI_API_KEY environment variable.")
        print("   You can get an API key from: https://platform.openai.com/api-keys")
        return False
    
    print(f"✓ OpenAI API key found: {api_key[:8]}...")
    
    # Run tests
    tests = [
        ("Voice Configuration", test_voice_configuration),
        ("Language Detection", test_language_detection),
        ("OpenAI TTS Direct", test_openai_tts_direct),
        ("Async TTS", test_async_tts),
        ("Integrated Response System", test_integrated_response_system),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Print summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your female voice multilingual AI system is working correctly.")
        print("\n📝 Summary of capabilities:")
        print("   • Female voice (nova) for both English and Hindi")
        print("   • Automatic language detection based on script")
        print("   • Appropriate voice selection based on detected language")
        print("   • Both sync and async TTS support")
        print("   • Integrated response system")
    else:
        print(f"\n⚠ {total - passed} test(s) failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())
