import asyncio
import json
import time
import uuid

from tutor.executors import static_responses
from tutor.modules.audio import text_speech
from tutor.modules.text import word_to_number
from tutor.modules.text.number_formatter import format_numbers_with_spaces
from tutor.modules.websocket import AudioCodec


class RequestHandler:
    def __init__(self, user, entry_dumper, logger):
        self.user = user
        self.entry_dumper = entry_dumper
        self.logger = logger
        self.is_completed = False
        self.last_llm_answer = ""
        self.audio_codec = AudioCodec()
        self.protocol_mode = False  # Track if using protocol mode

    def handle_final_transcript(self, user_input):
        while True:
            self.is_completed = False
            self.entry_dumper.start_dump_task("user", user_input)
            user_input = word_to_number.remove_symbols_except_period(user_input)
            self.user.sync_send(json.dumps({"type": "transcript_batch", "data": user_input}))
            # self.logger.info("Pausing audio recording...")
            self.user.sync_send(json.dumps({"type": "pause_audio_recording", "data": ""}))
            #print("User input:", user_input)
            self.logger.info(f"User input: {user_input}")

            is_amount = False
            llm_answer = None
            end_call = False

            start_time = time.time()
            # llm_answer = self.user.vehicle_validator.assistant.answer_call_back(user_input)
            # llm_answer = self.user.bot_manager.answer_call_back(user_input)
            llm_answer = self.answer_call_back(user_input)
            end_time = time.time()
            response_time = end_time - start_time
            # print("Response time of self.answer_call_back:", response_time)
            # print("AI response:", llm_answer)
            self.logger.info(f"Response time of self.answer_call_back: {response_time}")
            self.logger.info(f"AI response: {llm_answer}")
            self.entry_dumper.start_dump_task("llm", f"{llm_answer}, response_time {response_time:.2f}")

            status, result, llm_response = self.user.bot_manager.process_llm_answer(llm_answer)
            if status:
                static_response = static_responses.sending_user_request()
                # Send static response using protocol audio
                if hasattr(self.user, 'stream_id') and self.user.stream_id:
                    asyncio.run_coroutine_threadsafe(
                        self.text_to_protocol_audio_send(static_response),
                        self.user.loop
                    ).result()
                self.entry_dumper.start_dump_task("static_response", static_response)

                llm_answer = self.user.bot_manager.post_vehicle_number_and_preset_amount(llm_answer, result)
                self.entry_dumper.start_dump_task("api_response", llm_answer)
                if "error:" in llm_answer:
                    llm_answer = llm_answer.replace("error:", "")
                else:
                    end_call = True
                    llm_answer = static_responses.get_success_message(result['preset_amount'],
                                                                      result['vehicle_number'])
                    self.entry_dumper.start_dump_task("get_success_message", llm_answer)
                    time.sleep(1)
            elif llm_response:
                llm_answer = llm_response
            else:
                new_llm_answer, is_end_no_vehicle = self.user.bot_manager.handle_no_vehicle_case(llm_answer)
                if is_end_no_vehicle:
                    end_call = True
                    llm_answer = new_llm_answer
                    self.entry_dumper.start_dump_task("get_no_vehicle_message", llm_answer)

            # print("AI after validation response:", llm_answer)
            self.logger.info(f"AI after validation response: {llm_answer}")
            # llm_answer = self.user.bot_manager.format_vehicle_number(llm_answer)
            llm_answer = format_numbers_with_spaces(llm_answer)
            # Send audio response using protocol-compliant method only
            if hasattr(self.user, 'stream_id') and self.user.stream_id:
                asyncio.run_coroutine_threadsafe(
                    self.text_to_protocol_audio_send(llm_answer),
                    self.user.loop
                ).result()
            else:
                self.logger.warning("No stream_id available, cannot send protocol audio")
            
            self.last_llm_answer = llm_answer
            self.entry_dumper.start_dump_task("after_validation", llm_answer)
            if end_call:
                time.sleep(1)
                self.user.send_end_call()
            else:
                self.is_completed = True
            break


    def handle_partial_transcript(self, user_input):
        self.user.sync_send(json.dumps({"type": "transcript_batch", "data": user_input}))

    def answer_call_back(self, user_input) -> str:
        response = None
        if user_input:
            async def get_llm_answer():
                return await self.user.bot_manager.handle_user_input(user_input)

            user_loop = self.user.loop
            future = asyncio.run_coroutine_threadsafe(get_llm_answer(), user_loop)
            response = future.result()
        if not response:
            response = "Sorry, I didn't get that."
        return response
    
    async def text_to_protocol_audio_send(self, text: str) -> None:
        """Send text as protocol-compliant audio events"""
        if not text or not text.strip():
            self.logger.warning("Empty text provided for TTS conversion")
            return
            
        if not hasattr(self.user, 'stream_id') or not self.user.stream_id:
            self.logger.warning("No stream ID available for protocol audio sending")
            return
            
        try:
            self.user.ai_start_listening = False
            
            # Split text into manageable chunks
            text_chunks = [sentence.strip() for sentence in text.split('. ') if sentence.strip()]
            
            # Validate language based on the response text content
            validated_language = "hi" # self.validate_and_update_language(text)
            
            for i, chunk in enumerate(text_chunks):
                # Generate mark name for this chunk
                mark_name = f"chunk_{i}_{int(time.time() * 1000)}"
                
                # Use the validated language instead of just detected language
                detected_language = validated_language
                
                # Log language usage and any switches
                if not hasattr(self, '_last_used_language'):
                    self._last_used_language = detected_language
                    self.logger.info(f"Initial TTS language set to: '{detected_language}'")
                elif self._last_used_language != detected_language:
                    self.logger.info(f"Language switched from '{self._last_used_language}' to '{detected_language}'")
                    self._last_used_language = detected_language
                else:
                    self.logger.debug(f"Continuing with language '{detected_language}' for TTS generation")
                
                # Generate audio for the chunk with language support
                start_time = time.time()
                audio_stream, _ = await asyncio.to_thread(
                    text_speech.tts_instance.generate_audio, chunk, detected_language
                )
                end_time = time.time()
                
                self.logger.info(f"Generated audio for '{chunk}': {end_time - start_time:.2f}s")
                
                # Read all audio data
                audio_data = audio_stream.read()
                
                # Convert to protocol format if needed
                try:
                    self.logger.info(f"Converting audio data: length={len(audio_data)} bytes")
                    # The audio_stream is raw PCM at 44100Hz, need to convert to μ-law at 8kHz
                    protocol_audio = self.audio_codec.convert_audio_to_protocol_format(
                        audio_data, 
                        source_sample_rate=44100,
                        source_channels=1,
                        source_sample_width=2
                    )
                    self.logger.info(f"Protocol audio converted: length={len(protocol_audio)} bytes")
                    
                    # Send via protocol events (audio already converted to protocol format)
                    await self.user.send_media_event(protocol_audio, mark_name, is_protocol_format=True)
                    self.logger.info(f"Successfully sent media event for chunk: {mark_name}")
                except Exception as convert_error:
                    self.logger.error(f"Error converting audio to protocol format: {convert_error}")
                    self.logger.error(f"Audio data details: length={len(audio_data)}, type={type(audio_data)}")
                    # Skip sending this chunk rather than sending invalid data
                    continue
                
                # Small delay between chunks
                await asyncio.sleep(0.1)
            
            self.user.ai_start_listening = True
            
        except Exception as e:
            self.logger.error(f"Error in text_to_protocol_audio_send: {e}")
    
    def enable_protocol_mode(self) -> None:
        """Enable protocol-compliant audio processing"""
        self.protocol_mode = True
        self.logger.info("Protocol mode enabled for request handler")
    
    def disable_protocol_mode(self) -> None:
        """Disable protocol mode and use legacy processing"""
        self.protocol_mode = False
        self.logger.info("Protocol mode disabled for request handler")
    
    def validate_and_update_language(self, text: str) -> str:
        """Validate detected language against text content and update if needed
        
        Args:
            text: Text to validate language detection against
            
        Returns:
            Validated language code
        """
        current_language = getattr(self.user, 'detected_language', 'en')
        
        # Simple validation: check if text contains Devanagari characters
        has_devanagari = any(0x0900 <= ord(char) <= 0x097F for char in text if char.isalpha())
        has_latin = any(0x0041 <= ord(char) <= 0x007A for char in text if char.isalpha())
        
        expected_language = 'hi' if has_devanagari else 'en'
        
        # Update language if detection seems incorrect
        if current_language != expected_language:
            self.logger.info(f"Language validation: updating from '{current_language}' to '{expected_language}' based on text analysis")
            if hasattr(self.user, 'detected_language'):
                self.user.detected_language = expected_language
            return expected_language
        
        return current_language
