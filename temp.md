i want this application suport hindi and english language. listening support hindi and english language then based on user language, i want ai reponse in user language.
for example if user speak in hindi then ai response in hindi.
aws_transcribe_language_code support auto.
text to speech also support hindi and english. use OpenAI api.
i want female voice.
try with sample audio and then check response text.

(.venv) root@LAPTOP-R6HCM6NP:/home/<USER>/PycharmProjects/one_call/one_call_demo/one_call# python3 main.py
Starting WebSocket server on port 1802 with asyncio event loop
Aug-22-2025 12:19:57 PM - INFO - [websocket_server:user:188] - set_stream_id - Set stream ID: 
MZA969DF1940294D998BBDCC48E56F088B
Aug-22-2025 12:19:57 PM - INFO - [websocket_server:user:106] - start_ai_call_with_protocol - Starting AI call with protocol support
Aug-22-2025 12:19:57 PM - INFO - [websocket_server:user:152] - _run_async - Running asynchronous coroutine: <coroutine object sleep at 0x72c12a282030>
Aug-22-2025 12:19:57 PM - INFO - [websocket_server:realtime_transcription:112] - _init_google_client - Google Cloud Speech client initialized successfully
Aug-22-2025 12:19:57 PM - INFO - [websocket_server:transcription_factory:50] - create_transcription_service - Created google_cloud transcription service
Aug-22-2025 12:20:01 PM - INFO - [websocket_server:realtime_transcription:383] - on_open_impl - Google Cloud Speech Session ID: gcp_1755845401
Aug-22-2025 12:20:01 PM - INFO - [websocket_server:realtime_transcription:237] - connect - Google Cloud Speech streaming connection established
Aug-22-2025 12:20:02 PM - INFO - [websocket_server:request_handler:146] - text_to_protocol_audio_send - Generated audio for 'Hello Web, welcome to ICICI Bank! How can I assist you today?': 0.29s
Aug-22-2025 12:20:02 PM - INFO - [websocket_server:request_handler:153] - text_to_protocol_audio_send - Converting audio data: length=427134 bytes
Aug-22-2025 12:20:02 PM - INFO - [websocket_server:request_handler:161] - text_to_protocol_audio_send - Protocol audio converted: length=38743 bytes
Aug-22-2025 12:20:02 PM - INFO - [websocket_server:user:288] - send_mark_event - Sent mark event: chunk_0_1755845402521
Aug-22-2025 12:20:02 PM - INFO - [websocket_server:request_handler:165] - text_to_protocol_audio_send - Successfully sent media event for chunk: chunk_0_1755845402521
Aug-22-2025 12:20:27 PM - INFO - [websocket_server:request_handler:146] - text_to_protocol_audio_send - Generated audio for 'Hello Web, welcome to ICICI Bank! How can I assist you today?': 0.00s
Aug-22-2025 12:20:27 PM - INFO - [websocket_server:request_handler:153] - text_to_protocol_audio_send - Converting audio data: length=427134 bytes
Aug-22-2025 12:20:27 PM - INFO - [websocket_server:request_handler:161] - text_to_protocol_audio_send - Protocol audio converted: length=38743 bytes
Aug-22-2025 12:20:27 PM - INFO - [websocket_server:user:288] - send_mark_event - Sent mark event: chunk_0_1755845427478
Aug-22-2025 12:20:27 PM - INFO - [websocket_server:request_handler:165] - text_to_protocol_audio_send - Successfully sent media event for chunk: chunk_0_1755845427478
Aug-22-2025 12:20:36 PM - INFO - [websocket_server:user:127] - end_ai_call - Ending AI call
Aug-22-2025 12:20:36 PM - INFO - [websocket_server:user:152] - _run_async - Running asynchronous coroutine: <coroutine object User.close at 0x72c15861d070>
Aug-22-2025 12:20:36 PM - INFO - [websocket_server:realtime_transcription:437] - on_close_impl - Closing Google Cloud Speech Session
Aug-22-2025 12:20:36 PM - INFO - [websocket_server:realtime_transcription:275] - close - Google Cloud Speech streaming connection closed
Aug-22-2025 12:20:36 PM - ERROR - [websocket_server:realtime_transcription:173] - _process_responses - Google Cloud Speech API error: 499 Locally cancelled by application!
Aug-22-2025 12:20:36 PM - ERROR - [websocket_server:realtime_transcription:402] - on_error_impl - An error occurred: 499 Locally cancelled by application!
Aug-22-2025 12:20:36 PM - INFO - [websocket_server:realtime_transcription:433] - on_error_impl - Non-critical error, continuing operation
Aug-22-2025 12:20:36 PM - INFO - [websocket_server:user:152] - _run_async - Running asynchronous coroutine: <coroutine object User.cleanup at 0x72c15861d770>
Aug-22-2025 12:34:37 PM - INFO - [websocket_server:user:188] - set_stream_id - Set stream ID: MZ1D9135BD6EE943CCB70D74EA1147E754
Aug-22-2025 12:34:37 PM - INFO - [websocket_server:user:106] - start_ai_call_with_protocol - Starting AI call with protocol support
Aug-22-2025 12:34:37 PM - INFO - [websocket_server:user:152] - _run_async - Running asynchronous coroutine: <coroutine object sleep at 0x72c15861d7e0>
Aug-22-2025 12:34:38 PM - INFO - [websocket_server:realtime_transcription:112] - _init_google_client - Google Cloud Speech client initialized successfully
Aug-22-2025 12:34:38 PM - INFO - [websocket_server:transcription_factory:50] - create_transcription_service - Created google_cloud transcription service
Aug-22-2025 12:34:40 PM - INFO - [websocket_server:realtime_transcription:383] - on_open_impl - Google Cloud Speech Session ID: gcp_1755846280
Aug-22-2025 12:34:40 PM - INFO - [websocket_server:realtime_transcription:237] - connect - Google Cloud Speech streaming connection established
Aug-22-2025 12:34:41 PM - INFO - [websocket_server:request_handler:146] - text_to_protocol_audio_send - Generated audio for 'Hello Web, welcome to ICICI Bank! How can I assist you today?': 0.00s
Aug-22-2025 12:34:41 PM - INFO - [websocket_server:request_handler:153] - text_to_protocol_audio_send - Converting audio data: length=427134 bytes
Aug-22-2025 12:34:41 PM - INFO - [websocket_server:request_handler:161] - text_to_protocol_audio_send - Protocol audio converted: length=38743 bytes
Aug-22-2025 12:34:41 PM - INFO - [websocket_server:user:288] - send_mark_event - Sent mark event: chunk_0_1755846281305
Aug-22-2025 12:34:41 PM - INFO - [websocket_server:request_handler:165] - text_to_protocol_audio_send - Successfully sent media event for chunk: chunk_0_1755846281305
Aug-22-2025 12:35:02 PM - INFO - [websocket_server:user:127] - end_ai_call - Ending AI call
Aug-22-2025 12:35:02 PM - INFO - [websocket_server:user:152] - _run_async - Running asynchronous coroutine: <coroutine object User.close at 0x72c15861d070>
Aug-22-2025 12:35:02 PM - INFO - [websocket_server:realtime_transcription:437] - on_close_impl - Closing Google Cloud Speech Session
Aug-22-2025 12:35:02 PM - INFO - [websocket_server:realtime_transcription:275] - close - Google Cloud Speech streaming connection closed
Aug-22-2025 12:35:02 PM - ERROR - [websocket_server:realtime_transcription:173] - _process_responses - Google Cloud Speech API error: 499 Locally cancelled by application!
Aug-22-2025 12:35:02 PM - ERROR - [websocket_server:realtime_transcription:402] - on_error_impl - An error occurred: 499 Locally cancelled by application!
Aug-22-2025 12:35:02 PM - INFO - [websocket_server:realtime_transcription:433] - on_error_impl - Non-critical error, continuing operation
Aug-22-2025 12:35:02 PM - INFO - [websocket_server:user:152] - _run_async - Running asynchronous coroutine: <coroutine object User.cleanup at 0x72c15861d000>



(.venv) root@LAPTOP-R6HCM6NP:/home/<USER>/PycharmProjects/one_call/one_call_demo/one_call# python3 main.py
Starting WebSocket server on port 1802 with asyncio event loop
Aug-22-2025 12:35:46 PM - INFO - [websocket_server:user:188] - set_stream_id - Set stream ID: MZ536827BE8F49452190BA6CDF864F5F4F
Aug-22-2025 12:35:46 PM - INFO - [websocket_server:user:106] - start_ai_call_with_protocol - Starting AI call with protocol support
Aug-22-2025 12:35:46 PM - INFO - [websocket_server:user:152] - _run_async - Running asynchronous coroutine: <coroutine object sleep at 0x769e969bccf0>
Aug-22-2025 12:35:46 PM - INFO - [websocket_server:transcription_factory:50] - create_transcription_service - Created assemblyai transcription service
Aug-22-2025 12:35:47 PM - INFO - [websocket_server:realtime_transcription:45] - on_open - AssemblyAI Session ID: a95e18fc-c562-4c8b-b186-ae7bc0260cc7
Aug-22-2025 12:35:49 PM - INFO - [websocket_server:request_handler:146] - text_to_protocol_audio_send - Generated audio for 'Hello Web, welcome to ICICI Bank! How can I assist you today?': 0.18s
Aug-22-2025 12:35:49 PM - INFO - [websocket_server:request_handler:153] - text_to_protocol_audio_send - Converting audio data: length=427134 bytes
Aug-22-2025 12:35:49 PM - INFO - [websocket_server:request_handler:161] - text_to_protocol_audio_send - Protocol audio converted: length=38743 bytes
Aug-22-2025 12:35:49 PM - INFO - [websocket_server:user:288] - send_mark_event - Sent mark event: chunk_0_1755846348887
Aug-22-2025 12:35:49 PM - INFO - [websocket_server:request_handler:165] - text_to_protocol_audio_send - Successfully sent media event for chunk: chunk_0_1755846348887
Aug-22-2025 12:36:06 PM - INFO - [websocket_server:realtime_transcription:53] - on_data - <class 'assemblyai.types.RealtimeFinalTranscript'>: Hello.
Aug-22-2025 12:36:08 PM - INFO - [websocket_server:realtime_transcription:53] - on_data - <class 'assemblyai.types.RealtimeFinalTranscript'>: What is my current.
Aug-22-2025 12:36:10 PM - INFO - [websocket_server:request_handler:32] - handle_final_transcript - User input: Hello.What is my current.
Aug-22-2025 12:36:11 PM - INFO - [websocket_server:request_handler:46] - handle_final_transcript - Response time of self.answer_call_back: 0.****************
Aug-22-2025 12:36:11 PM - INFO - [websocket_server:request_handler:47] - handle_final_transcript - AI response: It seems you are asking about your current balance. Your account balance is Rupees 10,000.
Aug-22-2025 12:36:11 PM - INFO - [websocket_server:request_handler:81] - handle_final_transcript - AI after validation response: It seems you are asking about your current balance. Your account balance is Rupees 10,000.
Aug-22-2025 12:36:11 PM - INFO - [websocket_server:request_handler:146] - text_to_protocol_audio_send - Generated audio for 'It seems you are asking about your current balance': 0.18s
Aug-22-2025 12:36:11 PM - INFO - [websocket_server:request_handler:153] - text_to_protocol_audio_send - Converting audio data: length=256062 bytes
Aug-22-2025 12:36:11 PM - INFO - [websocket_server:request_handler:161] - text_to_protocol_audio_send - Protocol audio converted: length=23226 bytes
Aug-22-2025 12:36:11 PM - INFO - [websocket_server:user:288] - send_mark_event - Sent mark event: chunk_0_1755846371618
Aug-22-2025 12:36:11 PM - INFO - [websocket_server:request_handler:165] - text_to_protocol_audio_send - Successfully sent media event for chunk: chunk_0_1755846371618
Aug-22-2025 12:36:13 PM - INFO - [websocket_server:request_handler:146] - text_to_protocol_audio_send - Generated audio for 'Your account balance is Rupees 10,000.': 1.47s
Aug-22-2025 12:36:13 PM - INFO - [websocket_server:request_handler:153] - text_to_protocol_audio_send - Converting audio data: length=267326 bytes
Aug-22-2025 12:36:13 PM - INFO - [websocket_server:request_handler:161] - text_to_protocol_audio_send - Protocol audio converted: length=24248 bytes
Aug-22-2025 12:36:13 PM - INFO - [websocket_server:user:288] - send_mark_event - Sent mark event: chunk_1_1755846371920
Aug-22-2025 12:36:13 PM - INFO - [websocket_server:request_handler:165] - text_to_protocol_audio_send - Successfully sent media event for chunk: chunk_1_1755846371920
Aug-22-2025 12:36:28 PM - INFO - [websocket_server:user:127] - end_ai_call - Ending AI call
Aug-22-2025 12:36:28 PM - INFO - [websocket_server:user:152] - _run_async - Running asynchronous coroutine: <coroutine object User.close at 0x769e969bd8c0>
Aug-22-2025 12:36:32 PM - INFO - [websocket_server:realtime_transcription:130] - on_extra_session_information - on_extra_session_information: 41.088 seconds
Aug-22-2025 12:36:33 PM - INFO - [websocket_server:realtime_transcription:118] - on_close - Closing Session
Aug-22-2025 12:36:33 PM - INFO - [websocket_server:user:152] - _run_async - Running asynchronous coroutine: <coroutine object User.cleanup at 0x769e969bcf20>


read above log, assembly ai working but google not working properlt, find a problem and fix it. please ultrathink.

i done google configuration, so test audio to text, create a test file and test it.

Task: Implement AWS Transcribe for real-time streaming voice-to-text conversion. Make the solution configurable. The ultimate goal is to provide a production-ready alternative to AssemblyAI. please ultrathink.

Use the following reference code for the implementation plan: 
@RealtimeTranscription

**Constraints:**
1. Ensure the coding style and logic remain consistent with the existing codebase.
2. Provide a detailed step-by-step implementation plan.
3. Do not modify the structure of the project or introduce unnecessary code changes.
4. First execute command 'List(.)'

**Project Codebase References:**
@tutor/api/main.py
@tutor/api/routers.py
@tutor/api/server.py
@tutor/executors/processor.py
@tutor/executors/user.py
@tutor/modules/logger.py
@tutor/modules/models.py
@tutor/modules/utils/shared.py
@tutor/websocket_server.py
